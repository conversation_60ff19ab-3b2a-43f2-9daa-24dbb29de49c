{"name": "Bo<PERSON>", "version": "7.08.152", "private": true, "description": "An Ionic project", "homepage": "https://ionicframework.com/", "author": "Ionic Framework", "scripts": {"build": "ng build", "build-android": "ionic capacitor build android --prod && cp -r src/assets/audio/* ./android/app/src/main/res/raw && cp AndroidManifest.xml ./android/app/src/main && cp file_paths.xml ./android/app/src/main/res/xml/file_paths.xml", "build-android-server": "mkdir ./android/app/src/main/res/raw && cp -r src/assets/audio/* ./android/app/src/main/res/raw && cp AndroidManifest.xml ./android/app/src/main && cp file_paths.xml ./android/app/src/main/res/xml/file_paths.xml", "build-ios": "ionic capacitor build ios --prod && cp info.plist ./ios/App/App/info.plist", "change_app_name": "bash ./automation/changing_name.sh \"${CLIENT_NAME}\" \"${APP_ID}\"", "changing_version": "bash ./automation/changing_version.sh", "changing_target_sdk_version": "bash ./automation/changing_target_sdk_version.sh", "e2e": "ng e2e", "generate_assets": "npx capacitor-assets generate", "i18n:extract": "ngx-translate-extract --input ./src --output ./src/assets/i18n/{en,ar,he}.json --clean --format json", "i18n:init": "ngx-translate-extract --input ./src --output ./src/assets/i18n/template.json --key-as-default-value --replace --format json", "lint": "ng lint", "ng": "ng", "patch": "cd .. && cp -R olivery_clients/* delivery_app/", "prebuild_env": "eval \"echo export const environment = {\\\\\\n production: true,\\\\\\n db: \\'${CLIENT_DB}\\',\\\\\\n url:\\'${CLIENT_URL}\\',\\\\\\n }\\; > ./src/environments/environment.prod.ts\" && eval \"echo export const environment = {\\\\\\n production: false,\\\\\\n db: \\'${CLIENT_DB}\\',\\\\\\n url:\\'${CLIENT_URL}\\',\\\\\\n }\\; > ./src/environments/environment.ts\"", "start": "ng serve", "turn_on_capgo_deployment": "bash ./automation/turning_on_capgo_deployment.sh", "turn_on_capgo_deployment_for_ios": "bash ./automation/turning_on_capgo_deployment_for_ios.sh", "take_images": "bash ./automation/taking_images.sh ${CLIENT_DB} && cp -r ./resources/icon.png ./resources/icon-only.png && cp -r ./resources/login.png ./src/assets/logo.png&& cp -r ./resources/icon.png ./resources/icon-foreground.png && cp -r ./resources/icon.png ./resources/icon-background.png && cp -r ./resources/splash.png ./resources/splash-dark.png && rm -rf ./resources/icon.png", "take_images_for_ios": "bash ./automation/taking_images_for_ios.sh ${CLIENT_DB} && cp -r ./resources/icon.png ./resources/icon-only.png && cp -r ./resources/login.png ./src/assets/logo.png&& cp -r ./resources/icon.png ./resources/icon-foreground.png && cp -r ./resources/icon.png ./resources/icon-background.png && cp -r ./resources/splash.png ./resources/splash-dark.png && rm -rf ./resources/icon.png", "test": "ng test", "background_location_check": "python3 ./automation/background_location_edit.py ${CLIENT_DB}", "watch": "ng build --watch --configuration development"}, "dependencies": {"@angular/animations": "^16.2.12", "@angular/cdk": "^16.0.0", "@angular/common": "^16.0.0", "@angular/compiler": "^16.0.0", "@angular/core": "^16.0.0", "@angular/forms": "^16.0.0", "@angular/google-maps": "^16.2.14", "@angular/material": "^16.1.5", "@angular/platform-browser": "^16.0.0", "@angular/platform-browser-dynamic": "^16.0.0", "@angular/router": "^16.0.0", "@angular/service-worker": "^16.2.10", "@awesome-cordova-plugins/android-permissions": "^6.6.0", "@awesome-cordova-plugins/app-availability": "^6.8.0", "@awesome-cordova-plugins/core": "^6.7.0", "@awesome-cordova-plugins/in-app-browser": "^6.7.0", "@bartholomej/ngx-translate-extract": "^8.0.2", "@capacitor/android": "^6.0.0", "@capacitor/app": "6.0.1", "@capacitor/assets": "latest", "@capacitor/browser": "^6.0.2", "@capacitor/camera": "^6.0.2", "@capacitor/core": "^6.0.0", "@capacitor/device": "^6.0.1", "@capacitor/dialog": "6.0.1", "@capacitor/filesystem": "6.0.1", "@capacitor/geolocation": "6.0.1", "@capacitor/haptics": "6.0.1", "@capacitor/ios": "6.1.2", "@capacitor/keyboard": "6.0.2", "@capacitor/network": "^6.0.2", "@capacitor/plugin-migration-v5-to-v6": "^0.0.6", "@capacitor/share": "^6.0.2", "@capacitor/splash-screen": "^6.0.2", "@capacitor/status-bar": "6.0.1", "@capawesome-team/capacitor-file-opener": "^6.1.0", "@capgo/capacitor-updater": "6.1.20", "@capgo/cli": "4.34.6", "@ionic/angular": "^7.0.0", "@ionic/storage": "^4.0.0", "@ionic/storage-angular": "^4.0.0", "@ngrx/effects": "^16.1.0", "@ngrx/entity": "^16.1.0", "@ngrx/store": "^16.1.0", "@ngrx/store-devtools": "^16.1.0", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@zxing/library": "^0.21.3", "annyang": "^2.6.1", "capacitor-native-settings": "^6.0.1", "chart.js": "^4.4.9", "cordova-plugin-android-permissions": "^1.1.5", "cordova-plugin-system-alert-window-permission": "^0.0.6", "core-js": "^3.31.1", "dynamsoft-camera-enhancer": "^4.1.0", "hammerjs": "^2.0.8", "howler": "^2.2.4", "html5-qrcode": "^2.3.8", "ionicons": "^7.0.0", "merge-stream": "^2.0.0", "moment": "^2.29.4", "ng-circle-progress": "^1.7.1", "ngx-signaturepad": "^0.0.10", "onesignal-cordova-plugin": "5.2.3", "rxjs": "~7.8.0", "signature_pad": "^4.1.6", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.0.0", "@angular-eslint/builder": "^16.0.0", "@angular-eslint/eslint-plugin": "^16.0.0", "@angular-eslint/eslint-plugin-template": "^16.0.0", "@angular-eslint/schematics": "^16.0.0", "@angular-eslint/template-parser": "^16.0.0", "@angular/cli": "^16.2.11", "@angular/compiler": "^16.0.0", "@angular/compiler-cli": "^16.0.0", "@angular/language-service": "^16.0.0", "@capacitor/assets": "^3.0.1", "@capacitor/cli": "^6.0.0", "@ionic/angular-toolkit": "^9.0.0", "@types/howler": "^2.2.11", "@types/jasmine": "~4.3.0", "@types/lodash": "^4.17.6", "@types/node": "^12.11.1", "@typescript-eslint/eslint-plugin": "5.3.0", "@typescript-eslint/parser": "5.3.0", "eslint": "^7.26.0", "eslint-plugin-import": "2.22.1", "eslint-plugin-jsdoc": "30.7.6", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~4.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "ts-node": "^8.3.0", "typescript": "~5.0.2", "uuid": "11.0.3"}, "cordova": {"plugins": {"onesignal-cordova-plugin": {}}, "platforms": ["ios", "android"]}}