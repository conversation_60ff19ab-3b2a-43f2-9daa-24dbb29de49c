import { Component, Input, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { OdooJsonRPC } from 'src/app/services/odooJsonRPC';

export interface TrueBuyerResponse {
  delivered_count: number;
  returned_count: number;
  stuck_count: number;
}

@Component({
  selector: 'app-true-buyer-modal',
  templateUrl: './true-buyer-modal.component.html',
  styleUrls: ['./true-buyer-modal.component.scss'],
})
export class TrueBuyerModalComponent implements OnInit {
  @Input() phoneNumber!: string;
 
  trueBuyerResult: TrueBuyerResponse | null = null;
  isLoading = false;
  errorMessage = '';

  constructor(
    private modalCtrl: ModalController,
    private odooRPC: OdooJsonRPC,
    private translate: TranslateService
  ) {}

  ngOnInit() {
    this.checkTrueBuyer();
  }

  private checkTrueBuyer() {
    this.isLoading = true;
    this.errorMessage = '';

    this.odooRPC.call('rb_delivery.utility', 'check_true_buyer', [this.phoneNumber]).then(response => {
      this.isLoading = false;
      
      if (response.body.result && response.body.result.result && response.body.result.result.success) {
        this.trueBuyerResult = response.body.result.result.data;
      } else {
        this.errorMessage = response.body.result?.result?.error || this.translate.instant('FAILED_TO_LOAD_BUYER_DATA');
      }
    }).catch(error => {
      this.isLoading = false;
      this.errorMessage = this.translate.instant('FAILED_TO_LOAD_BUYER_DATA');
      console.error('True Buyer API error:', error);
    });
  }

  closeModal() {
    this.modalCtrl.dismiss();
  }
}