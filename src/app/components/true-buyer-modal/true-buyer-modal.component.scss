
.modal-content {
  background: white;
  border-radius: 20px 20px 0 0;
  min-height: 300px;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e0e0e0;
  background: white;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  
  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: black;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
  
  ion-button {
    z-index: 1;
  }
}

.modal-body {
  padding: 24px;
  min-height: 200px;
  width: 100%;
  background: white; 
  flex: 1;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.label {
  font-size: 16px;
  color: black;
  font-weight: 500;
}

.value {
  font-size: 16px;
  font-weight: 600;
  color: black;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  
  p {
    margin-top: 16px;
    color: #666;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #dc3545;
  
  ion-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
}

.modal-footer {
  padding: 16px 24px 24px;
  border-top: 1px solid #e0e0e0;
  background: white;
  width: 100%;
  box-sizing: border-box;
  margin-top: auto; 
}

.info-row {
  width: 100%;
}