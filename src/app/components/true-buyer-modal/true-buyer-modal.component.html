<div class="modal-content">
  <div class="modal-header">
    <h2>{{'CHECK_BUYER'|translate}}</h2>
    <ion-button fill="clear" (click)="closeModal()">
      <ion-icon name="close" slot="icon-only"></ion-icon>
    </ion-button>
  </div>

  <div class="modal-body">
    <div class="info-row">
      <span class="label">{{'MOBILE:'|translate}}</span>
      <span class="value">{{ phoneNumber }}</span>
    </div>

    <div *ngIf="isLoading" class="loading-container">
      <ion-spinner color="primary"></ion-spinner>
      <p>{{'CHECKING_BUYER_STATUS...'|translate}}</p>
    </div>

    <div *ngIf="trueBuyerResult && !isLoading" class="results-container">
      <div class="info-row">
        <span class="label">{{'DELIVERD_ORDERS:'|translate}}</span>
        <span class="value">{{ trueBuyerResult.delivered_count }}</span>
      </div>

      <div class="info-row">
        <span class="label">{{'STUCK_ORDERS:'|translate}}</span>
        <span class="value">{{ trueBuyerResult.stuck_count }}</span>
      </div>

      <div class="info-row">
        <span class="label">{{'ACTIVE_ORDERS:'|translate}}</span>
        <span class="value">{{ trueBuyerResult.returned_count }}</span>
      </div>
    </div>

    <div *ngIf="errorMessage" class="error-container">
      <ion-icon name="warning" color="danger"></ion-icon>
      <p>{{ errorMessage }}</p>
    </div>
  </div>

  <div class="modal-footer">
    <ion-button expand="block" fill="solid" color="primary" (click)="closeModal()">
      {{'CANCEL'|translate}}
    </ion-button>
  </div>
</div>