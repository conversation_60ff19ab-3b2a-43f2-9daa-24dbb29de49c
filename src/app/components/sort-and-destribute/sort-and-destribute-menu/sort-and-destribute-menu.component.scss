.flip-arrow {
    transform: scaleX(-1);
}

.button-content-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.close-btn {
    --padding-end: 0em;
    --padding-start: 0.3em;
    font-size:16px
}

/* Status Flow Visualization Styles */
.status-flow-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 0;
}

.status-rectangle {
    border-radius: 15px;
    padding: 8px 12px;
    min-width: 70px;
    max-width: 120px;
    text-align: center;
    box-shadow: rgba(50, 50, 93, 0.15) 0px 2px 5px -1px, rgba(0, 0, 0, 0.2) 0px 1px 3px -1px;
    transition: all 0.2s ease-in-out;
    flex: 1;
}

.current-status {
    background: linear-gradient(135deg, var(--ion-color-primary) 0%, #f2c750 100%);
    color: white;
    border: 2px solid var(--ion-color-primary-shade);
}

.next-status {
    background: linear-gradient(135deg, var(--ion-color-secondary) 0%, #24d6ea 100%);
    color: white;
    border: 2px solid var(--ion-color-secondary-shade);
}

.status-text {
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.status-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 4px;
}

.arrow-icon {
    font-size: 16px;
    color: var(--ion-color-medium);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0.8;
    }
}