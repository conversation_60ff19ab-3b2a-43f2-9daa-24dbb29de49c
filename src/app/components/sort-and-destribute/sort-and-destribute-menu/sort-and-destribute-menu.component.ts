import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>roller, ModalController, ModalOptions } from '@ionic/angular';
import { FollowOrderService } from 'src/app/services/follow-orders-service';
import { RecordStructureService } from 'src/app/services/record-structure-service';
import { OdooJsonRPC } from 'src/app/services/odooJsonRPC';
import { BarcodeScanComponent } from 'src/app/modals/barcode-scan/barcode-scan.component';
import { dialogService } from 'src/app/services/error-handlar-service';
import { TranslateService } from '@ngx-translate/core';
import { FollowerOrderScannerComponent } from '../../follower-order-scan/follower-order-scanner.component';


@Component({
  selector: 'app-sort-and-destribute-menu',
  templateUrl: './sort-and-destribute-menu.component.html',
  styleUrls: ['./sort-and-destribute-menu.component.scss'],
})
export class SortAndDestributeMenuComponent  implements OnInit {


  scannedOrders: any[] = []
  orders: any[] = []
  dir: string = '';

  followOrders: { sequence: string; originalSequence: string }[] = [];

  neededFields: any

  scanActivate = false

  scannedFollow: any[] = []

  prevent_change: boolean = false

  submitLocked: boolean = false

  ordersWithErrors: any[]=[]

  ordersScanner: { [index: string]: any | false } = {};
  

  constructor(
    private modalCtrl: ModalController,
    private followOrdersService: FollowOrderService,
    private recordStructureService : RecordStructureService,
    private odooRpc : OdooJsonRPC,
    private dialogService: dialogService,
    private translate : TranslateService,
    private alertCtrl: AlertController,
  ) {  }

  async ngOnInit() {
    this.dir = document.documentElement.getAttribute('dir') || 'ltr';
    await this.fetchRecordStructure()
  }

  async fetchRecordStructure() {
     await this.recordStructureService.fetchStructure('sort_and_destribute_card').then(structueResponse=>{
      if(structueResponse && structueResponse.success){
        this.neededFields = structueResponse.neededFields
      }
    })
  }


  async submit() { 
    this.submitLocked = true
    let toBeChanged:any[] = this.scannedOrders
    if (this.prevent_change) {
      for (let barcode of this.orders){ 
        let followOrdersNumber = this.getFollowOrdersNumber(barcode)
        let numberOfScanned = this.getNumberOfScannedFollowOrders(barcode)
        if(followOrdersNumber != numberOfScanned) {
          toBeChanged = toBeChanged.filter(order => order != barcode)
        }
      }
    }
      let Response = await this.odooRpc.call('rb_delivery.mobile_sort_and_distribute', 'sort_and_destribute_orders', [toBeChanged])
      if (Response?.body?.result?.result) {
        let orders_errors = Response.body.result.result
        if (typeof orders_errors === 'string') {
          this.dialogService.error({
            title: this.translate.instant('ERROR_IN_SORT_AND_DESTRIBUTE'),
            message: this.translate.instant(orders_errors),
            whatToDo: this.translate.instant(orders_errors),
            code: '1208',
          })
          this.submitLocked = false
        } else if (orders_errors && orders_errors.length > 0) {
          let error_message:number[] = []
          let originalLength = this.scannedOrders.length
          for (let order of orders_errors) {
            if (toBeChanged.includes(order[0])) {
              error_message.push(order[0])
            }
            if (toBeChanged.includes(order[1])) {
              error_message.push(order[1])
            }
            
        }
  
          let correctBarcodes = toBeChanged.filter(order => !error_message.includes(order))
  
          correctBarcodes.forEach(sequence=>{
            this.removeOrder(sequence, null, true)
          })
  
  
          if (error_message) {
            let error_message_str = error_message.join(', ');
            this.dialogService.error({
              title: this.translate.instant('ERROR_IN_SORT_AND_DESTRIBUTE'),
              message: this.translate.instant('ERROR_WHILE_SUBMITTING_SORT_AND_DESTRIBUTE_ORDERS_THERE_IS_NO_CONFIGURATION_TO_SORT_THE_FOLLOWING_ORDERS:') + error_message_str + this.translate.instant('NUMBER_OF_SUCCESS_IS:') + (correctBarcodes.length) + '/' + originalLength,
              whatToDo: this.translate.instant('TO_SOLVE_THE_ISSUE_GO_TO_OLIVERY_CONFIGURATION_THEN_MOBILE_CONFIGURATION_THEN_MOBILE_SORT_AND_DESTROBUTE_THEN_SELECT_THE_NEEDED_ROLE_AND_ADD_THE_CONFIGURATION_TO_IT'),
              code: '1207',
            })
          }
          this.submitLocked = false
        } else {
          if (toBeChanged.length < this.scannedOrders.length && this.prevent_change) {
            let originalLength = this.scannedOrders.length
            toBeChanged.forEach(sequence=>{
              this.removeOrder(sequence, null, true)
            })
            this.dialogService.error({
              title: this.translate.instant('ERROR_IN_SORT_AND_DESTRIBUTE'),
              message: this.translate.instant('ERROR_WHILE_SUBMITTING_SORT_AND_DESTRIBUTE_ORDERS_SUCSESS')+(toBeChanged.length) + '/' + originalLength,
              whatToDo: this.translate.instant('PLEASE_SCAN_ALL_FOLLOW_ORDERS_TO_BE_ABLE_TO_SUBMIT'),
              code: '1206',
            })
            this.submitLocked = false
          } else {
            this.submitLocked = false
            this.modalCtrl.dismiss()
          }
        }
      } else {
        let orders_errors = Response.body.error.data.arguments[0]
        this.dialogService.error({
          input: orders_errors,
          message: orders_errors,
          whatToDo: orders_errors,
          code: '1211',
        })
        this.submitLocked = false
      }
      this.ordersWithErrors = this.scannedOrders

  }


  async removeOrder(sequence: any, event?: Event | null, force?:boolean) {
    if (event){
      event.stopPropagation();
    }
    let message = 'ARE_YOU_SURE_YOU_WANT_TO_REMOVE_THIS_ORDER'
    if (this.checkIfFollowOrders(sequence)) {
      message+= '_IT_HAS_FOLLOW_ORDERS'
    }
    if (!force) {
      const alert = await this.alertCtrl.create({
        header: this.translate.instant('CONFIRMATION'),
        message: this.translate.instant(message),
        mode:'ios',
        buttons: [
          {
            text: this.translate.instant('CANCEL'),
            role: 'cancel',
            cssClass: 'secondary'
          },
          {
            text: this.translate.instant('REMOVE'),
            handler: () => {
              this.scannedOrders = this.scannedOrders.filter(order => order !== sequence);
              this.orders = this.orders.filter(order => order !== sequence);
              let orderFollow = this.followOrders.filter(follow => follow.originalSequence == sequence);
              for (let follow of orderFollow) {
                this.removeOrder(follow.sequence)
              }
              this.followOrders = this.followOrders.filter(follow => follow.originalSequence !== sequence);
              this.scannedFollow = this.scannedFollow.filter(follow => follow.originalSequence !== sequence);
            }
          }
        ]
      });
  
      await alert.present();
    } else {
      this.scannedOrders = this.scannedOrders.filter(order => order !== sequence);
      this.orders = this.orders.filter(order => order !== sequence);
      this.followOrders = this.followOrders.filter(follow => follow.originalSequence !== sequence);
      this.scannedFollow = this.scannedFollow.filter(follow => follow.originalSequence !== sequence);
    }
  }
  
  async scanMore() {
    await this.openBarcodeScanner({
      'mode': 'multi',
      'barcodes': this.scannedOrders,
      'orders': this.ordersScanner
    }).then(async scanResult=>{

      if(scanResult.data.scanningFinished){
        return
      }
      if(scanResult.data){
        if ( !this.scannedOrders.includes(scanResult.data.barcodes)) {
          for (let barcode of scanResult.data.barcodes) {
            this.openOrderRecordModal(barcode)
          }
        }
        this.ordersScanner = scanResult.data.orders
      }

    })
    
  }

  async addBarcode(barcode: string, notScanned?: boolean) {
    if (!notScanned) {
      this.scannedOrders.push(barcode)
    }
    let added = false
    if (!this.orders.includes(barcode)) {
      this.orders.push(barcode)
      added = true
    }
    if (!this.scannedFollow.includes(barcode) && this.followOrders.some(order => order.sequence === barcode)) {
      this.scannedFollow.push(barcode)
    }
    if (added) {
      let followOrders = await this.followOrdersService.getFollowSequences(barcode)
      if (followOrders) {
        const barcodes = followOrders.map((item: { follow_up_sequence: string }) => item.follow_up_sequence);
        for (let followOrder of barcodes) {
          
          this.followOrders.push({
            sequence: followOrder, 
            originalSequence: barcode
          })
        }
      }
      this.checkAlreadyScannedFollowOrders()
      this.filterScannedOrders()
    }
  }
  checkAlreadyScannedFollowOrders() {
    for (let scanned of this.scannedOrders) {
      if (this.followOrders.some(order => order.sequence === scanned) && !this.scannedFollow.includes(scanned)) {
        this.scannedFollow.push(scanned)
      }
    }
  }

  filterScannedOrders() {
    if (this.followOrders.length > 0) {
      let filteredOrders = this.scannedOrders.filter(orderSeq => 
        !this.followOrders.some(order => order.sequence === orderSeq)
      );
      this.scannedOrders = filteredOrders;

      let filteredAll =  this.orders.filter(orderSeq => 
        !this.followOrders.some(order => order.sequence === orderSeq)
      );
      this.orders = filteredAll
    }
  }


  openBarcodeScanner(componentProps={}): Promise<any> {
    this.scanActivate = true;
    return new Promise((resolve, reject) => {
      resolve({data:{'barcodes': ['73200000019293']}})
      // this.modalCtrl.create({
      //   component: BarcodeScanComponent,
      //   backdropDismiss:false,
      //   componentProps,
      //   cssClass:"exclude-hide"
      // }).then(modal => {
      //   modal.present();
      //   modal.onDidDismiss().then(scanResult => {
      //     this.scanActivate = false;
      //     resolve(scanResult);
      //   })
      // })
    });
  }

  openFollowOrdersList(originalSequence: string) {
    let followOrdersSequences = this.followOrders
      .filter(order => order.originalSequence === originalSequence)
      .map(order => order.sequence);
    let scannedFollowOrders = this.scannedFollow.filter(order => followOrdersSequences.includes(order))
    if (followOrdersSequences && followOrdersSequences.length != scannedFollowOrders.length) {
      let options: ModalOptions = {
        component: FollowerOrderScannerComponent,
        initialBreakpoint : 0.65,
        breakpoints :[0, 0.25, 0.5, 0.75,1],
        handleBehavior : 'cycle',
        cssClass:"follow-orders-list-dialog",
        componentProps: {
          originalSequence: originalSequence,
          disableScan: true,
          sequenceBarcodeList: followOrdersSequences,
          scannedBarcodes: scannedFollowOrders,
        }
      };
  
      this.modalCtrl.create(options).then(modal => {
        modal.present();
      });
    }
  }

  close() { 
    this.modalCtrl.dismiss()
  }

  checkIfFollowOrders(sequence: string) {
   return this.followOrders.some(order => order.originalSequence === sequence);
  }

  getFollowOrdersNumber(sequence: string): number {
    return this.followOrders.filter(order => order.originalSequence === sequence).length;
  }

  getNumberOfScannedFollowOrders(sequence: string): number {
    if (this.scannedFollow.length ==  0) 
      return 0
    this.scannedFollow = [...this.scannedFollow];

  
    let followOrdersSequences = this.followOrders
      .filter(order => order.originalSequence === sequence)
      .map(order => order.sequence);
  
    return this.scannedFollow.filter(order => followOrdersSequences.includes(order)).length;
  }

  getColorOfNumber(sequence: string){
    if (this.getFollowOrdersNumber(sequence) == this.getNumberOfScannedFollowOrders(sequence)) {
      return '#08B249'
    } 
    return '#FF3D00'
  }

  async openOrderRecordModal(order_sequence: string) {
    let order = await this.odooRpc.call('rb_delivery.mobile_sort_and_distribute', 'get_order_info', [order_sequence])
    if (order?.body?.result?.result == "MULTIPLE_ORDERS_FOUND_FOR_THE_SAME_REFERENCE") {
      this.dialogService.error({
        message: this.translate.instant('ERROR_WHILE_GETTING_ORDER_INFO'),
        title: this.translate.instant('ERROR_IN_SORT_AND_DESTRIBUTE'),
        whatToDo: this.translate.instant('MULTIPLE_ORDERS_FOUND_FOR_THE_SAME_REFERENCE'),
        code: '1210'
      })
      return
    }
    if (order && order.body && order.body.result && order.body.result.result &&  order.body.result.result.length == 0) {
      this.dialogService.error({
        message: this.translate.instant('ERROR_WHILE_GETTING_ORDER_INFO'),
        title: this.translate.instant('ERROR_IN_SORT_AND_DESTRIBUTE'),
        whatToDo: this.translate.instant('PLEASE_MAKE_SURE_THE_ORDER_EXISTS_OR_YOU_HAVE_ACCESS_TO_IT'),
        code: '1209'
      })
      this.scannedOrders = this.scannedOrders.filter(order => order != order_sequence)
      return;
    } else if (order && order.body && order.body.result && order.body.result.result &&  order.body.result.result.length > 0) {
      this.addBarcode(order_sequence)
      order = order.body.result.result[0]
      if (order.sequence && order.sequence != order_sequence && !this.orders.includes(order.sequence) && !this.scannedOrders.includes(order.sequence) && !this.orders.includes(order.reference_id) && !this.scannedOrders.includes(order.reference_id) && !this.orders.includes(order.follower_ref_id) && !this.scannedOrders.includes(order.follower_ref_id) && !this.orders.includes(order.partner_reference_id) && !this.scannedOrders.includes(order.partner_reference_id)) {
        this.addBarcode(order.sequence, true)
      }
    }
  }

  doFunction(functionName: OperationTypes) {
    if (functionName in this)
      this[functionName]()
  }


}

enum OperationTypes {
  close = 'close',
  scanMore = 'scanMore',
}