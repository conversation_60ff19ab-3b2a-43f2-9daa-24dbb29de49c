<ion-header [translucent]="true" mode="ios">
  <ion-toolbar style="padding-right: 0.4rem;" mode="ios">
      <ion-row>
          <ion-col size="1.5" style="align-items: center;
          display: flex;
          text-align: start;
          justify-content: start;">
              <ion-img style=" width: 35px;" src="../../../assets/icons/follow_up_orders.svg"> </ion-img>
          </ion-col>
          <ion-col size="9" style="display: flex; flex-direction: column;justify-content:center">
              <ion-row style="color: #333333; margin-bottom: 5px;">
                  <h3 style="font-size: small;">
                      {{ 'SORT_AND_DESTRIBUTE' | translate}}
                  </h3>
              </ion-row>
          </ion-col>
          <ion-col size="1.5" style="align-items: center;
          display: flex;
          text-align: end;
          justify-content: end;" class="ion-text-end">
              <ion-img (click)="close()" style=" width: 22px" [ngClass]="{'flip-arrow': dir === 'rtl'}"
                  src="../../../assets/icons/back_arrow.svg"></ion-img>
          </ion-col>
      </ion-row>
  </ion-toolbar>
</ion-header>

<ion-content style="background: transparent;z-index: -1;" >
  <ion-list>
      <ion-item *ngFor="let item of orders; let i = index" lines="none"
          style="margin: 2vh 4vw;box-shadow: rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;border-radius: 15px;border-color: blue;height: auto; min-height: 9vh; --padding-start: 0px !important;
          --inner-padding-end: 0px;
          --ion-safe-area-left: 0px;" (click)="openFollowOrdersList(item)">
          <ion-grid style="height: auto;display: flex;align-items: center;justify-content: center; padding: 15px; flex-direction: column;">
            <div style="width: 100%;">
              <ion-row style="align-items: center;">
                  <ion-col *ngIf="!scannedOrders.includes(item)" style="display: contents;"> 
                      <ion-img style=" width: 18pt;" src="../../../assets/icons/is_scaned.svg"></ion-img>
                  </ion-col>
                  <ion-col *ngIf="ordersWithErrors.includes(item)" style="display: contents;">
                      <ion-img style=" width: 18pt;" src="../../../assets/icons/is_scaned_no.svg"></ion-img>
                  </ion-col>
                  <ion-col *ngIf="scannedOrders.includes(item) && !ordersWithErrors.includes(item) " style="display: contents;">
                    <ion-img style=" width: 18pt;" src="../../../assets/icons/is_scaned_yes.svg"></ion-img>
                  </ion-col>

                  <ion-col style="font-size: small; font-weight: bold; color: black; display: flex; align-items: center; margin: 0px 2px;">
                      {{ item.length > 10 && checkIfFollowOrders(item) ? (item | slice:0:10) + '...' : item }}
                  </ion-col>
                  
                  <ion-col style="display: contents;" *ngIf="checkIfFollowOrders(item)">
                    <div style="display: flex;align-items: center;justify-content: center;gap: 5px;font-size: 10pt;">
                      <span style="align-self: center; color: black;">{{'FOLLOW_ORDERS' | translate}}</span>
                      <span style="align-self: center; font-weight: bold; color: black;">{{getFollowOrdersNumber(item)}}</span> <span style="align-self: center;"> / </span>
                      <span style="align-self: center; color: black; font-weight: bold;">{{getNumberOfScannedFollowOrders(item)}}</span>
                    </div>
                  </ion-col>

                  <ion-col style="display: contents;">
                    <ion-button fill="clear" class="close-btn" (click)="removeOrder(item, $event)">
                        <ion-img style=" width: 15pt;" src="../../../assets/icons/remove.svg"></ion-img>
                    </ion-button>
                  </ion-col>
              </ion-row>

              <!-- Status Flow Visualization - New Row -->
              <ion-row style="margin-top: 8px;" *ngIf="ordersData[item] && ordersData[item].to_state">
                <ion-col size="12">
                  <div class="status-flow-container">
                    <div class="status-rectangle current-status">
                      <span class="status-text">{{getCurrentStatus(item)}}</span>
                    </div>
                    <div class="status-arrow">
                      <ion-icon name="arrow-forward" class="arrow-icon"></ion-icon>
                    </div>
                    <div class="status-rectangle next-status">
                      <span class="status-text">{{getNextStatus(item)}}</span>
                    </div>
                  </div>
                </ion-col>
              </ion-row>
            </div>
          </ion-grid>
      </ion-item>
  </ion-list>

</ion-content>

<ion-footer class="ion-no-border" style="height: 9vh;box-shadow: rgba(14, 30, 37, 0.12) 0px 2px 4px 0px, rgba(14, 30, 37, 0.32) 0px 2px 16px 0px;">
  <ion-toolbar>
      <ion-row style="margin-bottom: 1rem;">
          <ion-col style="text-align: center;display: flex;">
              <ion-button style="width:90vw;text-transform: uppercase;" mode="ios" (click)="submit()" [disabled]="submitLocked"
              >{{'CONFIRM'
                  |
                  translate}}</ion-button>
                  <ion-button style="width:90vw;text-transform: uppercase;" mode="ios" (click)="scanMore()">
                    <div class="button-content-container">
                      <ion-img
                      src="../../../assets/icon/scan_icon.svg"> </ion-img>     
                      {{'SCAN' | translate}}
                    </div>
                  </ion-button>
          </ion-col>
      </ion-row>
  </ion-toolbar>
</ion-footer>