import { Component, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { debounceTime, SequenceError, Subject } from 'rxjs';
import { BarcodeScanComponent } from 'src/app/modals/barcode-scan/barcode-scan.component';
import { dialogService } from 'src/app/services/error-handlar-service';
import { OdooJsonRPC } from 'src/app/services/odooJsonRPC';
import { FollowerOrderScannerComponent } from '../follower-order-scan/follower-order-scanner.component';
import { FollowOrderService } from 'src/app/services/follow-orders-service';
import { StatusActionService } from 'src/app/services/statusAction';
import { Storage } from '@ionic/storage-angular';

@Component({
  selector: 'app-update-multi-orders',
  templateUrl: './update-multi-orders.component.html',
  styleUrls: ['./update-multi-orders.component.scss'],
})
export class UpdateMultiOrdersComponent  implements OnInit {

  state!:any
  expandedItems:string[] = []
  barcodeList:string[] = []
  referenceBarcodeList:string[] = []
  sequenceBarcodeList:string[] = []
  validBarcodes:string[] = []
  firstScanType!:string

  messages:any = {}

  disabled: boolean = false
  barcodesToFetch: any[] = []
  statuses: any = {}
  orderInfo: any = {}
  ValList: { [key: string]: any } = {};
  orderIds: { [key: string]: number } = {};
  userInfo: any;
  group_id: number = 0;
  useValList: boolean = false;

  private updateInputsSubject = new Subject<{ }>();

  constructor(
    private modalCtrl:ModalController,
    private translate:TranslateService,
    private dialogService: dialogService,
    private odooRpc: OdooJsonRPC,
    private followOrdersService: FollowOrderService,
    private statusActionService: StatusActionService,
    private storage: Storage

  ) { }

  ngOnInit() {
    this.initScanner()
  }

  async initScanner() {
    this.getUserInfo()
    await this.checkIfHasStatusAction()
    if(this.firstScanType== 'REFERENCE'){
      this.referenceBarcodeList.push(this.barcodeList[0])
    }
    else if(this.firstScanType== 'SEQUENCE'){
      this.sequenceBarcodeList.push(this.barcodeList[0])
    }
    this.validBarcodes.push(this.barcodeList[0])
    this.barcodesToFetch.push(this.barcodeList[0])
    this.setupInputUpdateStream()
    this.updateInputsSubject.next({})
  }
  async checkIfHasStatusAction() {
    const formResponse = await this.odooRpc.call('rb_delivery.mobile_form_input', 'get_form', ['status_action_form', this.state.id, 'ar_SY', false]);
    if (formResponse && formResponse.body && formResponse.body.result && formResponse.body.result.success && formResponse.body.result.result && formResponse.body.result.result.inputs && formResponse.body.result.result.inputs.length > 0) {
      this.useValList = true
    }
    return formResponse.body.result.success;
  }
  getUserInfo() {
    this.storage.get('user_info').then(userInfo=>{
      if(userInfo){
        this.userInfo = userInfo
        this.group_id = userInfo[0].group_id[0] || 0
      }
    })
  }


   async updateState(){
    this.disabled = true
    let values = {}
    let context = {}
    if (this.useValList) {
      values = this.ValList
      context = {'valListSent': true}
    } else {
      values = this.state
    }
    if (this.referenceBarcodeList.length == 0 && this.sequenceBarcodeList.length == 0) {
      this.dialogService.warning({
        input: this.translate.instant('PLEASE_SCAN_REFERENCE_BARCODE'),
        message: this.translate.instant('PLEASE_SCAN_REFERENCE_BARCODE_MESSAGE'),
        whatToDo: this.translate.instant('PLEASE_SCAN_REFERENCE_BARCODE_WHAT_TO_DO'),
        code: '1309'
      })
      this.disabled = false
      return
    }
    let result = await this.odooRpc.call('rb_delivery.order', 'bulk_change_state_by_barcode', [values,{'reference_id':this.referenceBarcodeList,'sequence':this.sequenceBarcodeList}], {'context': context})
    this.disabled = false
    if (result && result.body && result.body.result && result.body.result.result && result.body.result.result.length > 0) {
      let err_messages = result.body.result.result
      for (let msg of err_messages) {
        if(msg.reference_id) {
          this.messages[msg.reference_id] = msg.message
        }
        this.messages[msg.sequence] = msg.message
      }
    } else {
      if (result && result.body && result.body.error && result.body.error.data && result.body.error.data.message) {
        this.dialogService.warning({
          input: result.body.error.data.message,
          message: result.body.error.data.message,
        })
      } else {
        this.modalCtrl.dismiss()
      }
    }

  }


  async scanAndUpdateBarcodeList(){
    this.openBarcodeScanner().then( async scanResult=>{
      this.disabled = true
      if(scanResult.data.scanningFinished){
        this.disabled = false
        return
      }
      if(scanResult.data.scannedValue && !this.barcodeList.includes(scanResult.data.scannedValue)){

        this.barcodesToFetch.push(scanResult.data.scannedValue)
        this.updateInputsSubject.next({})
        let status = ''
        if (typeof this.state == 'object') {
          status = this.state.name
        } else {
          status = this.state
        }
       let goIntoFollowOrdersScan = await this.followOrdersService.validateIfShouldScanFollowOrders(status)
       let follower_obj = await this.followOrdersService.getFollowSequences(scanResult.data.scannedValue)

        if(follower_obj && follower_obj.length > 0 && goIntoFollowOrdersScan){
          const names = follower_obj.map((item: { name: string }) => item.name);
          const barcodes = follower_obj.map((item: { follow_up_sequence: string }) => item.follow_up_sequence);
          this.modalCtrl.create({
            component : FollowerOrderScannerComponent,
            cssClass:"hide-when-scan-barcode",
            componentProps :{
              change : false,
              originalSequence : scanResult.data.scannedValue,
              sequenceBarcodeList : barcodes,
              nameList: names,
            }}).then(modal =>{
              modal.present()

              modal.onDidDismiss().then(output => {
                console.log(output)
                if(scanResult.data.type == 'REFERENCE'){
                  this.referenceBarcodeList.push(scanResult.data.scannedValue as string)
                  this.firstScanType = 'REFERENCE'
                }
                else if(scanResult.data.type == 'SEQUENCE'){
                  this.sequenceBarcodeList.push(scanResult.data.scannedValue as string)
                  this.firstScanType = 'SEQUENCE'
                }
        
        
                this.validBarcodes.push(scanResult.data.scannedValue as string)
                this.barcodeList.push(scanResult.data.scannedValue as string)
                
                this.scanAndUpdateBarcodeList()
              })
            })
        } else {
          if(scanResult.data.type == 'REFERENCE'){
            this.referenceBarcodeList.push(scanResult.data.scannedValue as string)
          }
          else if(scanResult.data.type == 'SEQUENCE'){
            this.sequenceBarcodeList.push(scanResult.data.scannedValue as string)
          }
  
  
          this.validBarcodes.push(scanResult.data.scannedValue as string)
          this.barcodeList.push(scanResult.data.scannedValue as string)
          
          this.scanAndUpdateBarcodeList()
        }
      }else if(scanResult.data.scannedValue){
        this.dialogService.warning({
          input: this.translate.instant('BARCODE_ALREADY_SCANNED'),
          message: this.translate.instant('BARCODE_ALREADY_SCANNED'),
          whatToDo: this.translate.instant('PLEASE_SCAN_OTHER_BARCODE'),
          code: '1310'
        })
      }
      this.disabled = false
    })
  }


  openBarcodeScanner(): Promise<any> {
    return new Promise((resolve, reject) => {
      this.modalCtrl.create({
        component: BarcodeScanComponent,
        backdropDismiss:false,
        componentProps:{
          typeSelectionItems:['SEQUENCE','REFERENCE'],
          selectedType: this.firstScanType
        },
        cssClass:"exclude-hide"
      }).then(modal => {
        modal.present();
        modal.onDidDismiss().then(scanResult => {
          this.firstScanType = scanResult.data.type
          resolve(scanResult);
        })
      })
    });
  }

  async reEnterValues(item: string) {
    let statusActionValues = await this.statusActionService.getStatusActionFields(this.state.name,this.group_id,'olivery_order',false,item,item,this.orderIds[item]);
    if (statusActionValues != 'NO_STATUS_ACTION_IDS_FOUND') {
      statusActionValues['state'] = this.state.name;
      this.ValList[this.orderIds[item]] = statusActionValues;
      this.updateInputsSubject.next({});
    }
  }

  setupInputUpdateStream() {
    this.disabled = true;
    this.updateInputsSubject.pipe(
      debounceTime(1400)  
    ).subscribe(({  }) => {
      this.fetchStatuses()
    });
  }

  checkIfHasVals(orderInfo:any) {

    if (!orderInfo)
      return false
    return Object.values(orderInfo).some(value => value !== "" && value !== null && value !== undefined && value !== false);
  }
  objectEntries(obj: any): { key: string, value: any }[] {
    return obj ? Object.entries(obj).map(([key, value]) => ({ key, value })) : [];
  }

  async fetchStatuses() {
    let statuses = await this.odooRpc.call('rb_delivery.order', 'get_statuses', [this.barcodesToFetch], {'context': {'fetch_ids': true}});
    if (statuses && statuses.body && statuses.body.result && statuses.body.result.result) {
      let result = statuses.body.result.result
      if (result.barcode_state_map) {
        this.orderIds = { ...this.orderIds, ...result.ids };
        result = result.barcode_state_map;
      } else {
        this.useValList = false;
      }
      let isOrderInfo = Object.values(result).some(value => typeof value === 'object' && value !== null);
      if (isOrderInfo) {
        this.orderInfo = {};
        Object.entries(result).forEach(async ([barcode, data]) => {
          if (this.orderIds[barcode] != undefined && this.useValList) {
            let statusActionValues = await this.statusActionService.getStatusActionFields(this.state.name,this.group_id,'olivery_order',false,barcode,barcode,this.orderIds[barcode]);
            if (statusActionValues != 'NO_STATUS_ACTION_IDS_FOUND') {
              statusActionValues['state'] = this.state.name;
              this.ValList[this.orderIds[barcode]] = statusActionValues;
            }
          }
          if (data && typeof data === 'object') {
            const statusKey = Object.keys(data).find(key => key.toLowerCase() === 'state_id');
            
            if (statusKey) {
              this.statuses[barcode] = (data as Record<string, any>)[statusKey];
              
              const { [statusKey]: _, ...orderDetails } : any = data;
              this.orderInfo[barcode] = orderDetails;
            } else {
              this.orderInfo[barcode] = data;
            }
          }
        });
      } else {
        for (let barcode of Object.keys(result)) {
          let order_ids = this.orderIds
          if (this.orderIds[barcode] != undefined  && this.useValList) {
            let statusActionValues = await this.statusActionService.getStatusActionFields(this.state.name,this.group_id,'olivery_order',false,barcode,barcode,order_ids[barcode]);
            if (statusActionValues != 'NO_STATUS_ACTION_IDS_FOUND') {
              statusActionValues['state'] = this.state.name;
              this.ValList[this.orderIds[barcode]] = statusActionValues;
            }
          }
        }
        Object.assign(this.statuses, result);
      }
  
      this.barcodesToFetch = [];
    }
    this.disabled = false;
  }

  close(){
    this.modalCtrl.dismiss()
  }

  removeBarCodeItem(barcode : string){
    this.barcodeList = this.barcodeList.filter(barcodeItem=>barcodeItem!=barcode)
    this.validBarcodes = this.validBarcodes.filter(barcodeItem=>barcodeItem!=barcode)
    this.referenceBarcodeList = this.referenceBarcodeList.filter(barcodeItem=>barcodeItem!=barcode)
    this.sequenceBarcodeList = this.sequenceBarcodeList.filter(barcodeItem=>barcodeItem!=barcode)
    this.barcodesToFetch = this.barcodesToFetch.filter(barcodeItem=>barcodeItem!=barcode)
    delete this.statuses[barcode]
    delete this.messages[barcode]
    delete this.orderInfo[barcode]
    delete this.ValList[this.orderIds[barcode]]
    delete this.orderIds[barcode]
    this.updateInputsSubject.next({})
  }

  toggleExpand(item:string){
    if(this.expandedItems.includes(item)){
      this.expandedItems=this.expandedItems.filter((i:any)=>i!=item)
    }
    else{
      this.expandedItems.push(item)
    }
  }

}
