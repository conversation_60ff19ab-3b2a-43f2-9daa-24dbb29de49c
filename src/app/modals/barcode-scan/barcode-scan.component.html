<ion-content fullscreen>
    <ion-grid class="scanner-parent-container">
        <div class="scanner-container" [ngClass]="{ 'scanner-container-abs': mode === 'multi' && barcodes.length > 0 && !scannerLoading }">
            <div id="reader" #reader></div>
            <div class="scanner-overlay">
                <div class="scanner-guide"></div>
            </div>
        </div>

        <div class="button-container" [ngClass]="{ 'button-container-abs': mode === 'multi' && barcodes.length > 0 && !scannerLoading }">
            <ng-container *ngIf="scannerLoading">
                <ion-spinner name="crescent" class="spinner">
                </ion-spinner>
                <span class="spinner-text">{{'LOADING'|translate}}</span>
            </ng-container>
            <div *ngIf="!scannerLoading">
                <ion-button (click)="stopScanner()" class="stop-scan-button">
                    {{ 'STOP_SCANNING' | translate }}
                </ion-button>
                <ion-button (click)="changeMode()" class="stop-scan-button">
                    {{ 'CHANGE_TO' | translate }}: <span style="padding-inline: 2px;">{{ getTextSrc() | translate }}</span> <ion-img style="width: 20px;padding-inline: 4px;" [src]="getImageSrc()"></ion-img>
                </ion-button>
            </div>

            <div class="type-selection" *ngIf="typeSelectionItems.length > 0 && !scannerLoading">
                <ion-button *ngFor="let typeSelectionItem of typeSelectionItems" [ngStyle]="{
                  '--background': typeSelectionItem === selectedType
                    ? 'var(--ion-color-success)'
                    : 'var(--ion-color-primary)'
                }" (click)="selectedType = typeSelectionItem">
                    {{ typeSelectionItem | translate }}
                </ion-button>
            </div>
        </div>

        <div *ngIf="mode === 'multi' && barcodes.length > 0 && !scannerLoading" class="barcode-list" [ngClass]="{ 'flash': flash }" #barcodeList>
            <h2 style="color:white;display: flex;align-items: center;justify-content: center;" ><ng-container>{{'SCANNED_BARCODES' | translate}}</ng-container> ({{ barcodes.length }})</h2>
            <div class="barcode-list-scrollable" [ngClass]="{ 'flash': flash }">
                <ion-list [ngClass]="{ 'flash': flash }">
                    <ion-item *ngFor="let barcode of barcodes; let i = index" [ngClass]="{ 'flash-item': flash }">
                        {{ i + 1 }}.
                        <ng-container (click)="openRecordCard(orders[barcode])">
                            <ion-label style="display: flex;
                                            padding-inline-start: 15px;">
                                <ion-img style="width: 30px;padding-inline: 4px;" src="../../../assets/icon/multi_scanner_sequence.svg"></ion-img>
                                {{ barcode }}
                            </ion-label>
                    
                            <ion-label style="display: flex;
                                        padding-inline-start: 15px;">
                                <ion-img style="width: 30px;padding-inline: 4px;" src="../../../assets/icon/multi_scanner_areas.svg"></ion-img>
                                <ion-col *ngIf="!orders.hasOwnProperty(barcode) && !orders[barcode]">
                                    <ion-spinner duration="3" style="zoom: .5;"/>
                                </ion-col>
                                {{ orders[barcode].customer_area[1] }}
                            </ion-label>
                            <ion-icon (click)="openRecordCard(orders[barcode])" src="/assets/icon/openLink.svg" style="font-size: x-large;padding: 5px;"></ion-icon>
                        </ng-container>
                    </ion-item>
                </ion-list>
            </div>
        </div>        
    </ion-grid>
</ion-content>