import { AfterViewInit, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { ModalController, Platform } from '@ionic/angular';
import { Camera } from '@capacitor/camera';
import { Haptics, ImpactStyle } from '@capacitor/haptics';
import { Html5Qrcode, Html5QrcodeCameraScanConfig, Html5QrcodeFullConfig, Html5QrcodeResult, Html5QrcodeSupportedFormats } from 'html5-qrcode';
import { Storage } from '@ionic/storage-angular';
import { OdooJsonRPC } from 'src/app/services/odooJsonRPC';
import { ClientConfigurationState } from '../../ngrx-store/client-configuration/store/state';
import * as clientConfigurationsSelector from '../../ngrx-store/client-configuration/store/selectors';
import { Store } from '@ngrx/store';
import { filter, Subject, take, takeUntil } from 'rxjs';
import { RecordCardStructure } from 'src/app/services/record-structure-service';
import { RecordItemComponent } from 'src/app/components/record-item/record-item.component';
import { RecordStructureService } from 'src/app/services/record-structure-service';


@Component({
  selector: 'app-barcode-scan',
  templateUrl: './barcode-scan.component.html',
  styleUrls: ['./barcode-scan.component.scss'],
})
export class BarcodeScanComponent implements OnInit, AfterViewInit {
  @Input() mode: 'single' | 'multi' = 'single';
  @ViewChild('reader') reader!: ElementRef | undefined;
  @ViewChild('barcodeList', { static: false }) barcodeList!: ElementRef;
  barcodes: string[] = [];
  html5QrcodeScanner: Html5Qrcode | undefined;
  @Input() showCompleteWithoutBarcodeButton: boolean = false;
  flash: boolean = false;
  userInfo: any;
  selectedType: any;
  scannerLoading: boolean = true;
  isScanning: boolean = false;
  modeName: string = 'BARCODE';
  activeModal:any

  typeSelectionItems: string[] = [];

  orders:{ [index: string]: any | false } = {};

  constructor(
    private modalCtrl: ModalController,
    private platform: Platform,
    private odooRpc: OdooJsonRPC,
    private storage: Storage,
    private clientConfigurationStore: Store<ClientConfigurationState>,
    private recordStructureService : RecordStructureService,
  ) {}

  ngAfterViewInit(): void {}

  ngOnInit() {

  }

  ionViewDidEnter() {
    if (this.typeSelectionItems.length > 0 && !this.selectedType) {
      this.selectedType = this.typeSelectionItems[0];
    }
    let started = false
    this.getUserInfo();
    this.clientConfigurationStore.select(clientConfigurationsSelector.selectClientConfigurationByKeys(['use_qr_code_by_default_in_mobile_scanner'])).pipe(
      filter((keys) => keys), 
      take(1)
    ).subscribe(configurationKeys => {
      if (configurationKeys[0].key == 'use_qr_code_by_default_in_mobile_scanner') {
        if (configurationKeys[0].value) {
          this.changeMode()
        } else {
          this.startScanner()
        }

        started = true
      }
    })
    if (!started) {
      this.startScanner()
    }
    this.barcodes = [...new Set(this.barcodes)];
  }

  getUserInfo() {
    this.storage.get('user_info').then((userInfo) => {
      if (userInfo) {
        this.userInfo = userInfo;
      }
    });
  }

  playSound(fileName:string) {
    const audio = new Audio('/assets/audio/' + fileName);
    audio.play();
  }

  triggerVibration() {
    Haptics.impact({
      style: ImpactStyle.Heavy,
    });
  }

  async selectHighestResolutionCamera(): Promise<string | null> {
    try {
      const cameras = await Html5Qrcode.getCameras();
      if (cameras && cameras.length) {
        let selectedCamera = cameras[0];
        cameras.forEach((camera) => {
          if (camera.label && camera.label.toLowerCase().includes('ultra')) {
            selectedCamera = camera;
          }
        });
        console.log('Selected Camera:', selectedCamera);
        return selectedCamera.id;
      }
    } catch (err) {
      console.error('Error getting cameras:', err);
    }
    return null;
  }

  async startScanner() {
    const permissions = await Camera.checkPermissions();
    if (permissions.camera === 'granted' && permissions.photos === 'granted') {
      if (!this.reader) return;

      const qrCodeRegionId = this.reader.nativeElement.id;

      // Set supported formats and qrbox size based on the current mode.
      const formatsToSupport = this.modeName === 'QR_CODE'
        ? [Html5QrcodeSupportedFormats.QR_CODE]
        : [Html5QrcodeSupportedFormats.CODE_128,Html5QrcodeSupportedFormats.CODE_39];

      const configOrVerbosityFlag: Html5QrcodeFullConfig = {
        verbose: true,
        formatsToSupport: formatsToSupport,
        useBarCodeDetectorIfSupported: this.modeName !== 'QR_CODE',
        experimentalFeatures: { useBarCodeDetectorIfSupported: this.modeName !== 'QR_CODE' },
      };

      let qrbox = this.modeName === 'QR_CODE'
        ? { width: 300, height: 300 }
        : { width: 300, height: 100 };

      this.html5QrcodeScanner = new Html5Qrcode(qrCodeRegionId, configOrVerbosityFlag);

      const config: Html5QrcodeCameraScanConfig = {
        fps: 1,
        qrbox: qrbox,
        videoConstraints: {
          facingMode: { exact: "environment" },
          width: { ideal: 1280 },
          height: { ideal: 720 },
        },
      };

      this.isScanning = false;

      try {
        let selectedCameraId: any;
        if (this.platform.is('android')) {
          selectedCameraId = await this.selectHighestResolutionCamera();
        } else {
          selectedCameraId = { facingMode: "environment" };
        }
        if (!selectedCameraId) {
          console.error('No camera found, aborting scanner start.');
          return;
        }

        this.html5QrcodeScanner
          .start(
            selectedCameraId,
            config,
            (decodedText: string, decodedResult: Html5QrcodeResult) => {
              if (!this.isScanning) return;

              if (!this.barcodes.includes(decodedText)) {
                this.barcodes.push(decodedText);

                if (this.mode == 'multi') {
                  this.fetchOrderDetails(decodedText)
                }
                
                let objectToCreate = {
                  scanned_by: this.userInfo?.[0]?.user_id?.[0],
                  scanned_value: decodedText,
                  scanned_in: 'Mobile',
                  scanned_type: this.selectedType,
                };

                this.odooRpc.call('rb_delivery.scan_logs', 'create', [objectToCreate]);
                console.log('Scanned Barcode:', decodedText);
                
                // If in single mode, stop scanning after the first successful scan
                if (this.mode === 'single') {
                  this.stopScanner();
                } else {
                  // In multi mode, continue scanning
                  setTimeout(() => {
                    this.isScanning = true;
                  }, 800);
                }
              } else {
                this.triggerFlashEffect();
              }
            },
            this.onScanFail
          )
          .then(() => {
            this.scannerLoading = false;
            console.log('Camera opened successfully!');
            setTimeout(() => {
              this.isScanning = true;
            }, 800);
          })
          .catch((err) => {
            console.error('Error starting scanner:', err);
          });
      } catch (err) {
        console.error('Error in startScanner:', err);
      }
    } else {
      const newPermissions = await Camera.requestPermissions();
      if (newPermissions.camera === 'granted' && newPermissions.photos === 'granted') {
        this.startScanner();
      }
    }
  }

  onScanFail(error: string) {
    console.error(error);
  }

  openRecordCard(order: any) {
    let buttonClickDetector = new Subject<any>();
    let destroyed = new Subject<any>();
  
    let structuredOrder: RecordCardStructure = this.recordStructureService.formatRecordForCard('sort_and_destribute_card', order) as RecordCardStructure;
  
    if (structuredOrder.footer) {
      structuredOrder.footer = [
        {
          button_theme: "big",
          function_name: "close",
          invisible_domain: false,
          label: "CLOSE",
          local_compute_function: false,
          color: 'danger'
        }
      ];
    }
    structuredOrder.header = undefined;
  
    if (this.activeModal) {
      this.slideOutModal(this.activeModal).then(() => {
        this.createAndShowModal(order, structuredOrder, buttonClickDetector, destroyed);
      });
    } else {
      this.createAndShowModal(order, structuredOrder, buttonClickDetector, destroyed);
    }
  }
  
  createAndShowModal(order: any, structuredOrder: any, buttonClickDetector: Subject<any>, destroyed: Subject<any>) {
    this.modalCtrl.create({
      component: RecordItemComponent,
      cssClass: "windowed-modal-sort-and-destribute fit-window sliding-modal",
      componentProps: {
        recordCardStructure: structuredOrder,
        structuresList: structuredOrder,
        removeMargin: true,
        showInOneLine: true,
        buttonClickDetector: buttonClickDetector,
        tmpSequence: order.sequence
      }
    }).then(modal => {
      this.activeModal = modal;
      modal.present().then(() => {
        const modalElement = document.querySelector('.sliding-modal') as HTMLElement;
        if (modalElement) {
          setTimeout(() => {
            modalElement.classList.add('slide-out');
            setTimeout(() => {
              modal.dismiss();
              this.activeModal = null;
            }, 1000); 
          }, 8000); 
        }
      });
  
      buttonClickDetector.pipe(takeUntil(destroyed)).subscribe(event => {
        this.close();
        if (event.functionName == 'close') return;
        this.doFunction(event.functionName);
      });
  
      modal.onDidDismiss().then(() => {
        destroyed.next({});
        destroyed.unsubscribe();
        buttonClickDetector.unsubscribe();
        this.activeModal = null;
      });
    });
  }
  
  slideOutModal(modal: any) {
    return new Promise<void>(resolve => {
      const modalElement = document.querySelector('.sliding-modal') as HTMLElement;
      if (modalElement) {
        modalElement.classList.add('slide-out');
        setTimeout(() => {
          modal.dismiss();
          resolve();
        }, 2000);
      } else {
        resolve();
      }
    });
  }    

  close() { 
    this.modalCtrl.dismiss()
  }

  doFunction(functionName: OperationTypes) {
    if (functionName in this)
      this[functionName]()
  }

  async fetchOrderDetails(sequence:string) {
    let order = await this.odooRpc.call('rb_delivery.mobile_sort_and_distribute', 'get_order_info', [sequence]);
    if (order.body && order.body.result && order.body.result.result && order.body.result.result[0] && order.body.result.result[0].customer_area && order.body.result.result[0].customer_area[1]) {
      this.orders[sequence] = order.body.result.result[0]
      this.openRecordCard(order.body.result.result[0])
      this.playSound('success_scanning.wav'); 
      this.triggerVibration();
    } else {
      this.removeOrder(sequence)
      this.playSound('fail_sound_1.mp3');
      this.triggerVibration();
    }
  }

  async stopScanner() {
    if (this.html5QrcodeScanner) {
      try {
        await this.html5QrcodeScanner.stop();
        this.html5QrcodeScanner.clear();
        
        if (this.barcodes.length === 0) {
          this.modalCtrl.dismiss({ scanningFinished: true });
          return;
        }
        
        if (this.mode === 'multi') {
          // Return all scanned barcodes
          if (this.selectedType) {
            this.modalCtrl.dismiss({ scannedValues: this.barcodes, type: this.selectedType });
          } else {
            this.modalCtrl.dismiss({'barcodes': this.barcodes, 'orders': this.orders});
          }
        } else {
          // Return only the first scanned barcode
          if (this.selectedType) {
            this.modalCtrl.dismiss({ scannedValue: this.barcodes[0], type: this.selectedType });
          } else {
            this.modalCtrl.dismiss(this.barcodes[0]);
          }
        }
      } catch (err) {
        this.modalCtrl.dismiss({ scanningFinished: true });
        console.error('Error stopping scanner:', err);
      } finally {
        this.html5QrcodeScanner = undefined;
        this.reader = undefined;
      }
    } else {
      if (this.mode === 'multi') {
        if (this.selectedType) {
          this.modalCtrl.dismiss({ scannedValues: this.barcodes, type: this.selectedType });
        } else {
          this.modalCtrl.dismiss(this.barcodes);
        }
      } else {
        if (this.selectedType && this.barcodes.length > 0) {
          this.modalCtrl.dismiss({ scannedValue: this.barcodes[0], type: this.selectedType });
        } else if (this.barcodes.length > 0) {
          this.modalCtrl.dismiss(this.barcodes[0]);
        } else {
          this.modalCtrl.dismiss({ scanningFinished: true });
        }
      }
    }
  }

  removeOrder(sequence:string) {
    this.barcodes = this.barcodes.filter(order => order !== sequence);
  }

  triggerFlashEffect() {
    this.flash = true;
    setTimeout(() => {
      this.flash = false;
    }, 300);
  }

  async completeWithoutBarcode() {
    if (this.html5QrcodeScanner) {
      try {
        await this.html5QrcodeScanner.stop();
        this.html5QrcodeScanner.clear();
        this.modalCtrl.dismiss('complete');
      } catch (err) {
        console.error('Error completing without barcode:', err);
      }
    }
  }

  async changeMode() {
    this.modeName = this.modeName === 'QR_CODE' ? 'BARCODE' : 'QR_CODE';
    this.scannerLoading = true

    if (this.html5QrcodeScanner) {
      try {
        await this.html5QrcodeScanner.stop();
        this.html5QrcodeScanner.clear();
      } catch (error) {
        console.error('Error stopping scanner during mode change:', error);
      }
      this.html5QrcodeScanner = undefined;
    }

    this.startScanner();
  }

  getImageSrc() {
    return this.modeName === 'BARCODE' ? '../../../assets/icon/qr_icon.svg' : '../../../assets/icon/barcode_icon.svg';
  }

  getTextSrc() {
    return this.modeName === 'BARCODE' ? 'QRCODE' : 'BARCODE';
  }
}


enum OperationTypes {
  close = 'close'
}