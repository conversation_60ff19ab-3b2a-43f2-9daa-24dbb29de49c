import { AfterViewInit, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { ModalController, Platform } from '@ionic/angular';
import { Camera } from '@capacitor/camera';
import { Haptics, ImpactStyle } from '@capacitor/haptics';
import { Html5Qrcode, Html5QrcodeCameraScanConfig, Html5QrcodeFullConfig, Html5QrcodeResult, Html5QrcodeSupportedFormats } from 'html5-qrcode';
import { Storage } from '@ionic/storage-angular';
import { OdooJsonRPC } from 'src/app/services/odooJsonRPC';

@Component({
  selector: 'folllow-orders-scanner',
  templateUrl: './folllow-orders-scanner.component.html',
  styleUrls: ['./folllow-orders-scanner.component.scss'],
})

export class FollowOrdersScanner  implements OnInit,AfterViewInit {
  @Input({required:true}) mode!: 'single' | 'multi';
  @ViewChild('reader') reader!: ElementRef | undefined;
  @ViewChild('barcodeList', { static: false }) barcodeList!: ElementRef;
  barcodes: string[] = [];
  html5QrcodeScanner: Html5Qrcode | undefined;
  @Input() showCompleteWithoutBarcodeButton: boolean = false;
  flash: boolean = false;
  userInfo: any;
  selectedType: any;
  scannerLoading: boolean = true;
  isScanning: boolean = false;
  modeName:string = 'BARCODE'

  typeSelectionItems: string[] = [];

  constructor(
    private modalCtrl: ModalController,
    private platform: Platform,
    private odooRpc: OdooJsonRPC,
    private storage: Storage,
  ) {}

  ngAfterViewInit(): void {}

  ngOnInit() {}

  ionViewDidEnter() {
    if (this.typeSelectionItems.length > 0 && !this.selectedType) {
      this.selectedType = this.typeSelectionItems[0];
    }
    this.getUserInfo();
    this.startScanner();
  }

  getUserInfo() {
    this.storage.get('user_info').then((userInfo) => {
      if (userInfo) {
        this.userInfo = userInfo;
      }
    });
  }

  playSound() {
    const audio = new Audio('/assets/audio/success_scanning.wav');
    audio.play();
  }

  triggerVibration() {
    Haptics.impact({
      style: ImpactStyle.Heavy,
    });
  }

  async selectHighestResolutionCamera(): Promise<string | null> {
    try {
      const cameras = await Html5Qrcode.getCameras();
      if (cameras && cameras.length) {
        let selectedCamera = cameras[0];
        cameras.forEach((camera) => {
          if (camera.label && camera.label.toLowerCase().includes('ultra')) {
            selectedCamera = camera;
          }
        });
        console.log('Selected Camera:', selectedCamera);
        return selectedCamera.id;
      }
    } catch (err) {
      console.error('Error getting cameras:', err);
    }
    return null;
  }

  async startScanner() {
    const permissions = await Camera.checkPermissions();
    if (permissions.camera === 'granted' && permissions.photos === 'granted') {
      if (!this.reader) return;

      const qrCodeRegionId = this.reader.nativeElement.id;

      const formatsToSupport = this.modeName === 'QR_CODE'
      ? [Html5QrcodeSupportedFormats.QR_CODE]
      : [Html5QrcodeSupportedFormats.CODE_128,Html5QrcodeSupportedFormats.CODE_39];

      const configOrVerbosityFlag: Html5QrcodeFullConfig = {
        verbose: true,
        formatsToSupport: formatsToSupport,
        useBarCodeDetectorIfSupported: this.modeName !== 'QR_CODE',
        experimentalFeatures: { useBarCodeDetectorIfSupported: this.modeName !== 'QR_CODE' },
      };

      let qrbox = this.modeName === 'QR_CODE'
        ? { width: 300, height: 300 }
        : { width: 300, height: 100 };

      this.html5QrcodeScanner = new Html5Qrcode(qrCodeRegionId, configOrVerbosityFlag);

      const config: Html5QrcodeCameraScanConfig = {
        fps: 1,
        qrbox: qrbox,
        videoConstraints: {
          facingMode: { exact: "environment" },
          width: { ideal: 4096 },
          height: { ideal: 2160 },
        },
      };

      this.isScanning = false;

      try {
        let selectedCameraId: any;
        if (this.platform.is('android')) {
          selectedCameraId = await this.selectHighestResolutionCamera();
        } else {
          selectedCameraId = { facingMode: "environment" };
        }
        if (!selectedCameraId) {
          console.error('No camera found, aborting scanner start.');
          return;
        }

        this.html5QrcodeScanner
          .start(
            selectedCameraId,
            config,
            (decodedText: string, decodedResult: Html5QrcodeResult) => {
              if (!this.isScanning) return;

              if (!this.barcodes.includes(decodedText)) {
                this.barcodes.push(decodedText);
                this.playSound();
                let objectToCreate = {
                  scanned_by: this.userInfo[0].user_id[0],
                  scanned_value: decodedText,
                  scanned_in: 'Mobile',
                  scanned_type: this.selectedType,
                };

                this.odooRpc.call('rb_delivery.scan_logs', 'create', [objectToCreate]);
                console.log('Scanned Barcode:', decodedText);
                this.stopScanner();
              } else {
                this.triggerFlashEffect();
              }
            },
            this.onScannFail
          )
          .then(() => {
            this.scannerLoading = false;
            console.log('Camera opened successfully!');
            setTimeout(() => {
              this.isScanning = true;
            }, 800);
          })
          .catch((err) => {
            console.error('Error starting scanner:', err);
          });
      } catch (err) {
        console.error('Error in startScanner:', err);
      }
    } else {
      const newPermissions = await Camera.requestPermissions();
      if (newPermissions.camera === 'granted' && newPermissions.photos === 'granted') {
        this.startScanner();
      }
    }
  }

  onScannFail(error: string) {
    console.error(error)
  }

  async stopScanner() {
    if (this.html5QrcodeScanner) {
      try {
        await this.html5QrcodeScanner.stop();
        this.html5QrcodeScanner.clear();
        if (!this.barcodes.length) {
          this.modalCtrl.dismiss({scanningFinished:true})
        }

        this.modalCtrl.dismiss(this.barcodes[0])
      } catch (err) {
        this.modalCtrl.dismiss({scanningFinished:true})
        console.error('Error stopping scanner:', err);
      } finally {
        this.html5QrcodeScanner = undefined;
        this.reader = undefined
      }
    }
    else{
      this.modalCtrl.dismiss(this.barcodes[0])
    }
  }
  triggerFlashEffect() {
    this.flash = true;
    setTimeout(() => {
      this.flash = false;
    }, 300);
  }

  async completeWithoutBarcode() {
    if (this.html5QrcodeScanner) {
      try {
        await this.html5QrcodeScanner.stop();
        this.html5QrcodeScanner.clear();
        this.modalCtrl.dismiss('complete');
      } catch (err) {
        console.error('Error completing without barcode:', err);
      }
    }
  }

  async changeMode() {
    this.modeName = this.modeName === 'QR_CODE' ? 'BARCODE' : 'QR_CODE';

    if (this.html5QrcodeScanner) {
      try {
        await this.html5QrcodeScanner.stop();
        this.html5QrcodeScanner.clear();
      } catch (error) {
        console.error('Error stopping scanner during mode change:', error);
      }
      this.html5QrcodeScanner = undefined;
    }

    this.startScanner();
  }

  getImageSrc(){
    return this.modeName === 'BARCODE' ? '../../../assets/icon/qr_icon.svg' : '../../../assets/icon/barcode_icon.svg'
  }

  getTextSrc(){
    return this.modeName === 'BARCODE' ? 'QRCODE' : 'BARCODE'
  }
}