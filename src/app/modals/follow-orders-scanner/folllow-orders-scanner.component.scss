/* Remove default padding and use Flexbox to stretch the scanner area. */
ion-content {
  --padding-top: 0;
  --padding-bottom: 0;
  display: flex;
  flex-direction: column;
}

/* Scanner takes all available space. */
.scanner-container {
  position: relative;
  width: 100%;
  background-color: black;
  overflow: hidden;
}

/* Make the video area (#reader) fill the container completely. */
#reader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Overlay for the scanning area. */
.scanner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.scanner-guide {
  width: 250px;
  height: 250px;
  position: relative;
}

/* Example line overlay on top of video */
#reader::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 50px;
  left: 50px;
  height: 2px;
  width: calc(100vw - 110px);
  background-color: red;
  transform: translateY(-50%);
  display: flex;
  justify-content: center;
  align-items: center;
}

.scanner-parent-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background: black;
}

.spinner{
  width: 100vw; 
  height: 15vh; 
  margin-top: 30vh;
  color: white !important;
}

.spinner-text{
  display: block;
  text-align: center;
  margin-top: 10px;
  color: white !important;
}

/* Button container below the scanner */
.button-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
}

/* Row of type selection buttons */
.type-selection {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

/* (Optional) Additional styling for your barcode list, flashing, etc. */
.barcode-list {
  margin-top: 20px;
  transition: background-color 0.5s ease;
}

.barcode-list-scrollable {
  max-height: 150px;
  overflow-y: auto;
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 8px;
  background-color: #F9F9F9;
}

.barcode-list.flash {
  background-color: rgba(235, 47, 47, 0.576);
}

.barcode-list-scrollable.flash {
  background-color: rgba(235, 47, 47, 0.402);
}

.flash {
  background-color: rgba(235, 47, 47, 0.402);
}
.flash-item {
  --background: rgba(235, 47, 47, 0.402);
}

/* iPad media queries (adjust if desired) */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait),
       (min-width: 1024px) and (max-width: 1366px) and (orientation: landscape) {
  .scanner-container {
    /* Perhaps you want a different height strategy for iPad */
    position: relative;
    width: 100%;
    background-color: black;
    overflow: hidden;
  }
  #reader {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
  #reader::after {
    content: "";
    position: absolute;
    top: 50%;
    right: 50px;
    left: 50px;
    height: 2px;
    width: calc(100vw - 110px);
    background-color: red;
    transform: translateY(-50%);
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
