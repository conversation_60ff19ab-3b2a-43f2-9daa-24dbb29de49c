<ion-content fullscreen>


    <ion-grid class="scanner-parent-container">
        <div class="scanner-container">
            <div id="reader" #reader></div>
            <div class="scanner-overlay">
                <div class="scanner-guide"></div>
            </div>
        </div>

        <div class="button-container">
            <ng-container *ngIf="scannerLoading">
                <ion-spinner name="crescent"  class="spinner">
                </ion-spinner>
                <span class="spinner-text">{{'LOADING'|translate}}</span>
              </ng-container>
              <div>
                <ion-button (click)="stopScanner()" class="stop-scan-button">
                    {{ 'STOP_SCANNING' | translate }}
                </ion-button>
                <ion-button (click)="changeMode()" class="stop-scan-button">
                    {{ 'CHANGE_TO' | translate }}:  <span style="padding-inline: 2px;">{{ getTextSrc() | translate }}</span> <ion-img style=" width: 20px;padding-inline: 4px;" [src]="getImageSrc()"></ion-img>
                </ion-button>
            </div>
        </div>
    </ion-grid>
</ion-content>
