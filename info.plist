<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>This app requires bluetooth for Thermal printer, and print waybill.</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>This app requires bluetooth for Thermal printer, and print waybill.</string>
	<key>NSMotionUsageDescription</key>
        <string>This app require NSMotionUsageDescription for calculating distance between target and destination</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
        <string>We use GPS to track driver while delivering the product, Hence, it sends a message to the customer when the driver reach his destination.</string>
	<key>NSPhotoLibraryUsageDescription</key>
        <string>Gallery access needed to scan barcode from images.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
        <string>Gallery access needed to add photo attachments when filling forms.</string>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<string>YES</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Always allow location access to help us efficiently manage deliveries</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Allow location access while using the app to enhance delivery coordination</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>olivery_appx</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>Enables the scanning of various barcodes of orders, to search and edit orders easier.</string>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
</dict>
</plist>
