const fs = require('fs');
const path = require('path');

function uncommentCodeInFiles(directory, searchStrings) {
  fs.readdir(directory, (err, files) => {
      if (err) {
          console.error(`Error reading directory ${directory}: ${err}`);
          return;
      }

      files.forEach(async file => {
          const filePath = path.join(directory, file);
          fs.stat(filePath, (err, stats) => {
              if (err) {
                  console.error(`Error reading file ${filePath}: ${err}`);
                  return;
              }

              if (stats.isDirectory()) {
                  uncommentCodeInFiles(filePath, searchStrings); // Recursive call for subdirectories
              } else if (stats.isFile()) {
                  if (file.endsWith('.ts') || file.endsWith('.js')) { // Adjust file types as needed
                      fs.readFile(filePath, 'utf-8', (err, data) => {
                          if (err) {
                              console.error(`Error reading file ${filePath}: ${err}`);
                              return;
                          }
                          let newData=data
                          for(let searchString of searchStrings){
                            // Regular expression to match both single-line and multi-line comments
                            newData = newData.replace(new RegExp(`\\/\\/.*${searchString}|\\/\\*(.|\\n)*?${searchString}.*?\\*\\/(\\s*?)`, 'g'), searchString);
                            
                          }
                          if (newData !== data) {
                            fs.writeFile(filePath, newData, err => {
                                if (err) {
                                    console.error(`Error writing to file ${filePath}: ${err}`);
                                } else {
                                    console.log(`Uncommented code in file: ${filePath}`);
                                }
                            });
                          }
                      });
                  }
              }
          });
      });
  });
}






function addPlugins(pluginsToAdd) {
    const packageFile = 'package.json';
  
    try {
      // Read the current package.json file
      const config = JSON.parse(fs.readFileSync(packageFile, 'utf-8'));
  
      // Add specified plugins
      if (!config.dependencies) {
        config.dependencies = {};
      }
  
      for (const plugin of pluginsToAdd) {
        config.dependencies[plugin.name] = plugin.version;
        console.log(`Added plugin: ${plugin.name}`);
      }
  
      // Write the modified configuration back to the file
      fs.writeFileSync(packageFile, JSON.stringify(config, null, 2));
      console.log('package.json updated successfully.');
    } catch (error) {
      console.error('Error occurred:', error);
    }
  }
  
  // List of plugins to add
  const pluginsToAdd = [{name:'@transistorsoft/capacitor-background-fetch',version:'^6.0.0'}, {name:'@transistorsoft/capacitor-background-geolocation',version:'^6.1.0'},{name:'@capawesome-team/capacitor-android-battery-optimization',version:'^6.0.0'}];
  
  // Call the function with the list of plugins to add
  addPlugins(pluginsToAdd);

  // Provide the root directory of your project and the string to search for
  const rootDirectory = 'src'; // Adjust this to your project root
  const searchStrings = ['import { BackgroundLocationService }','BackgroundLocationService,','private backgroundLocationService','this.backgroundLocationService', 'await this.backgroundLocationService']; // Adjust this to your search string
    uncommentCodeInFiles(rootDirectory, searchStrings);
